import { useState, useEffect } from 'react'
import { IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT } from './constants'
import { useRef } from 'react'
import { WorldMap } from '../../../shared/types/World'
import { isoToScreen } from './coordinateUtils'
import { FOG_OF_WAR_TEXTURES, FRAME_DURATION, CAMERA_UI_UPDATE_INTERVAL } from './constants'
import { CameraRef, createKeyDownHandler, createContextMenuHandler, createWheelHandler } from './eventHandlers'


/**
 * Хук для адаптивного размера экрана с максимальными ограничениями.
 * Возвращает { width, height } для canvas.
 */
export function useAdaptiveScreenSize(maxWidth: number = IMCOMP_MAX_WIDTH, maxHeight: number = IMCOMP_MAX_HEIGHT) {
  const [screenSize, setScreenSize] = useState({
    width: Math.min(window.innerWidth, maxWidth),
    height: Math.min(window.innerHeight, maxHeight)
  })

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: Math.min(window.innerWidth, maxWidth),
        height: Math.min(window.innerHeight, maxHeight)
      })
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [maxWidth, maxHeight])

  return screenSize
}
/**
 * Хуки для изометрической карты
 */
/**
 * Хук для инициализации изображения тумана войны
 */
export const useFogOfWarImages = () => {
  const fogImagesRef = useRef<HTMLImageElement[]>([])

  useEffect(() => {
    const images: HTMLImageElement[] = FOG_OF_WAR_TEXTURES.map(src => {
      const img = new Image()
      img.src = src
      return img
    })
    fogImagesRef.current = images
  }, [])

  return fogImagesRef
}


/**
 * Хук для центрирования камеры на карте
 */
export const useCameraCenter = (
  currentWorld: WorldMap | null,
  cameraRef: React.RefObject<CameraRef>,
  tileWidth: number,
  tileHeight: number,
  draw: () => void
) => {
  useEffect(() => {
    if (!cameraRef.current) return

    // центр карты в тайлах (по изометрии)
    const mapSize = currentWorld?.settings?.worldSize || 20
    const centerTile = mapSize / 2

    // конвертим в экранные координаты центр карты
    const centerScreen = isoToScreen(centerTile, centerTile, tileWidth, tileHeight)

    // ставим камеру так, чтобы центр карты был в середине канваса
    cameraRef.current.x = centerScreen.x
    cameraRef.current.y = centerScreen.y

    draw()
  }, [currentWorld, tileWidth, tileHeight, draw]) // ТОЛЬКО currentWorld в зависимостях!
}

/**
 * Хук для циклической отрисовки
 */
export const useRenderLoop = (draw: () => void, zoom: number) => {
  useEffect(() => {
    let animationFrameId: number
    let lastRenderTime = 0

    const renderLoop = (time: number) => {
      if (time - lastRenderTime >= FRAME_DURATION) {
        draw()
        lastRenderTime = time
      }
      animationFrameId = requestAnimationFrame(renderLoop)
    }

    animationFrameId = requestAnimationFrame(renderLoop)

    return () => cancelAnimationFrame(animationFrameId)
  }, [zoom])
}

/**
 * Хук для обновления UI камеры
 */
export const useCameraUI = (
  cameraRef: React.RefObject<CameraRef>,
  setCameraUI: (camera: { x: number; y: number }) => void
) => {
  useEffect(() => {
    const interval = setInterval(() => {
      if (cameraRef.current) {
        setCameraUI({
          x: Math.round(cameraRef.current.x),
          y: Math.round(cameraRef.current.y)
        })
      }
    }, CAMERA_UI_UPDATE_INTERVAL)

    return () => clearInterval(interval)
  }, [cameraRef, setCameraUI])
}

/**
 * Хук для обработки клавиш
 */
export const useKeyboardControls = (cameraRef: React.RefObject<CameraRef>) => {
  useEffect(() => {
    const handleKeyDown = createKeyDownHandler(cameraRef)
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [cameraRef])
}

/**
 * Хук для обработки колеса мыши
 */
export const useWheelControls = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  zoom: number,
  handleZoomChange: (newZoom: number) => void
) => {
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const wheelHandler = createWheelHandler(zoom, handleZoomChange)
    canvas.addEventListener('wheel', wheelHandler, { passive: false })
    return () => canvas.removeEventListener('wheel', wheelHandler)
  }, [canvasRef, zoom, handleZoomChange])
}

/**
 * Хук для отключения контекстного меню
 */
export const useContextMenuDisable = (canvasRef: React.RefObject<HTMLCanvasElement>) => {
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const handler = createContextMenuHandler()
    canvas.addEventListener('contextmenu', handler)
    return () => canvas.removeEventListener('contextmenu', handler)
  }, [canvasRef])
}

/**
 * Хук для начальной отрисовки
 */
export const useInitialDraw = (draw: () => void) => {
  useEffect(() => {
    draw()
  }, [draw])
}
