

export const IMCOMP_MAX_WIDTH = 1920
export const IMCOMP_MAX_HEIGHT = 990
/**
 * Константы для изометрической карты
 */

// Константы для изометрической проекции
export const BASE_TILE_WIDTH = 70
export const BASE_TILE_HEIGHT = 46

// Настройки рендеринга
export const TARGET_FPS = 60
export const FRAME_DURATION = 1000 / TARGET_FPS

// Настройки камеры
export const CAMERA_MOVE_SPEED = 10

// Настройки зума
export const MIN_ZOOM = 1
export const MAX_ZOOM = 2
export const ZOOM_STEP = 0.1

// Настройки тайлов
export const TILE_GAP = -1 // размер отступа между тайлами

// Настройки UI обновления
export const CAMERA_UI_UPDATE_INTERVAL = 10 // мс

// Пути к ресурсам
export const FOG_OF_WAR_TEXTURES = [
  `/textures/worldMap/fogOfWar/fogOfWar-1.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-2.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-3.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-4.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-5.jpg`,
  `/textures/worldMap/fogOfWar/fogOfWar-6.jpg`
]
export const PLAYER_WM_TEXTURES = [
  `/textures/worldMap/player/WMPlayer-1.png`,
  `/textures/worldMap/player/WMPlayer-2.png`,
  `/textures/worldMap/player/WMPlayer-3.png`,
  `/textures/worldMap/player/WMPlayer-4.png`
].map((src) => {
  const img = new Image();
  img.src = src;
  return img;
});



// Настройки анимации тумана войны
export const FOG_ANIMATION_SPEED = 0.02// Скорость анимации (0.01 = медленно, 0.05 = быстро)
export const FOG_TRANSITION_DURATION = 5100 // Длительность перехода между состояниями в мс
export const FOG_IMAGE_TRANSITION_DURATION = 500 // Длительность анимации одного изображения в мс
export const FOG_FLICKER_PERIOD = 500 // Период моргания тумана войны (мс)
export const FOG_FLICKER_INTENSITY = 0.15 // Интенсивность мерцания (0.1 = слабо, 0.3 = сильно)
