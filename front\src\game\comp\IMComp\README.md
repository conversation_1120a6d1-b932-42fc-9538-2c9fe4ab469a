
# Быстрый старт: I<PERSON><PERSON><PERSON> (IsometricMap Components)

Модуль для изометрической карты. Всё разбито по категориям — ищи нужное по названию файла:

## Категории

- **constants.ts** — константы (размеры тайлов, FPS, камера, ресурсы)
- **coordinateUtils.ts** — координаты (iso/screen, tile size, tile visibility)
- **renderUtils.ts** — отрисовка (цвет тайла, тайл, туман войны)
- **eventHandlers.ts** — события (мышь, zoom, колесо, клики, клавиши, контекстное меню)
- **hooks.ts** — хуки (ту<PERSON><PERSON><PERSON> войны, центр камеры, рендер-цикл, UI камеры, управление)
- **drawEngine.ts** — движок рендера (функция отрисовки карты)
- **MapControls.tsx** — UI-компоненты (инфо камеры, управление зумом)
- **index.ts** — экспорт всего

## Импорт

```typescript
import {
  BASE_TILE_WIDTH,
  BASE_TILE_HEIGHT,
  getScaledTileSize,
  useFogOfWarImage,
  useCameraCenter,
  useRenderLoop,
  createDrawFunction,
  MapControls,
  ZoomControls
} from './IMComp'
```

## Преимущества

- Модульность: всё по категориям
- Переиспользование: утилиты и хуки можно брать в других компонентах
- Тестируемость: каждый модуль тестируется отдельно
- Читаемость: основной файл стал короче
- Поддерживаемость: изменения локализованы
